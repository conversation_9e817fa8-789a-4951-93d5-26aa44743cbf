"""
Unified Anchor Management Classes

This module provides classes for managing both anchor geometry and parameter
configurations in a single unified interface.
"""
import logging
import tkinter as tk
from tkinter import messagebox, ttk
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import logging

# Import shared utilities and constants
from .common import (
    FONT_REGULAR, FONT_BOLD, FONT_HEADER, BG_COLOR, ACCENT_COLOR, ERROR_COLOR,
    ENTRY_WIDTH, BUTTON_WIDTH, COMBO_WIDTH, ANCHOR_TYPES, UNIT_OPTIONS,
    STANDARD_COORDINATE_LABELS, SETTLEMENT_COLUMNS, SETTLEMENT_DEFAULT_VALUES,
    safe_float_conversion, create_primary_button, create_danger_button,
    create_standard_frame, update_button_state, handle_error_with_logging
)

# Import existing classes for geometry management
from .anchor_geom_class import CoordinateSet, GTypeCoordinateSet, AnchorSection

# Import existing classes for parameter management
from .anchor_param_class import AnchorConfig, UIComponentData, SettlementData


@dataclass
class UnifiedAnchorData:
    """Data structure for unified anchor configuration."""
    name: str
    anchor_type: str
    geometry_data: Dict[str, Any]
    parameter_data: Dict[str, Any]
    stage: Optional[str] = None


class UnifiedAnchorSection:
    """
    Manages both geometry and parameter configuration for a single anchor.
    
    This class extends the functionality of AnchorSection to include
    parameter management capabilities.
    """
    
    def __init__(self, parent_frame: tk.Frame, anchor_name: str, row: int, 
                 ui_components_manager=None, anchor_type: str = "N"):
        self.parent_frame = parent_frame
        self.anchor_name = anchor_name
        self.row = row
        self.anchor_type = anchor_type
        self.ui_components_manager = ui_components_manager
        
        # Geometry components (from AnchorSection)
        self.coordinate_sets = []
        self.anchor_name_var = None
        self.anchor_name_entry = None
        
        # Parameter components
        self.parameter_ui_components = {}
        
        # Main frame and UI elements
        self.frame = None
        self.geometry_frame = None
        self.parameter_frame = None
        self.add_button = None
        self.remove_button = None
        
        # Synchronization flag
        self._syncing = False
        
        self.create_ui()
    
    def create_ui(self):
        """Create the unified UI for both geometry and parameters."""
        # Create main frame for this anchor
        self.frame = tk.LabelFrame(self.parent_frame,
                                  text=f"Anchor_{self.anchor_name} Configuration",
                                  font=FONT_HEADER,
                                  bg=BG_COLOR,
                                  pady=10)
        self.frame.grid(row=self.row, column=0, sticky="ew", padx=10, pady=10)
        
        # Configure grid weight for proper expansion
        self.frame.grid_columnconfigure(0, weight=1)
        
        # Create geometry section
        self.create_geometry_section()
        
        # Create parameter section
        self.create_parameter_section()
        
        # Create control buttons
        self.create_control_buttons()
    
    def create_geometry_section(self):
        """Create the geometry configuration section."""
        # Geometry section header
        self.geometry_frame = tk.LabelFrame(self.frame, text="Geometry Configuration",
                                          font=FONT_BOLD, bg=BG_COLOR)
        self.geometry_frame.grid(row=0, column=0, sticky="ew", padx=5, pady=5)
        self.geometry_frame.grid_columnconfigure(0, weight=1)
        
        # Add anchor name entry field
        anchor_name_frame = tk.Frame(self.geometry_frame, bg=BG_COLOR)
        anchor_name_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=5)
        
        tk.Label(anchor_name_frame, text="Anchor Name:", font=FONT_BOLD,
                bg=BG_COLOR).pack(side=tk.LEFT, padx=5)
        
        self.anchor_name_var = tk.StringVar()
        self.anchor_name_entry = tk.Entry(anchor_name_frame, textvariable=self.anchor_name_var,
                                        width=20, font=FONT_REGULAR)
        self.anchor_name_entry.pack(side=tk.LEFT, padx=5)
        
        # Add initial coordinate set
        self.add_coordinate_set()
    
    def create_parameter_section(self):
        """Create the parameter configuration section."""
        # Parameter section header
        self.parameter_frame = tk.LabelFrame(self.frame, text="Parameter Configuration",
                                           font=FONT_BOLD, bg=BG_COLOR)
        self.parameter_frame.grid(row=1, column=0, sticky="ew", padx=5, pady=5)
        self.parameter_frame.grid_columnconfigure(0, weight=1)
        
        # Create parameter UI based on anchor type
        self.create_parameter_ui_for_type()
    
    def create_parameter_ui_for_type(self):
        """Create parameter UI components based on anchor type."""
        if self.anchor_type in ["N", "F"]:
            self.create_nf_parameter_ui()
        elif self.anchor_type == "G":
            self.create_g_parameter_ui()
    
    def create_nf_parameter_ui(self):
        """Create parameter UI for N and F type anchors."""
        # Create parameter entries frame
        param_entries_frame = tk.Frame(self.parameter_frame, bg=BG_COLOR)
        param_entries_frame.grid(row=0, column=0, sticky="ew", padx=5, pady=5)
        
        # Create subframes for different parameter types
        section_unit_frame = tk.Frame(param_entries_frame, width=180, bg=BG_COLOR)
        section_unit_frame.grid(row=0, column=0, padx=5, sticky="n")
        section_unit_frame.grid_propagate(False)
        
        spacing_frame = tk.Frame(param_entries_frame, width=140, bg=BG_COLOR)
        spacing_frame.grid(row=0, column=1, padx=5, sticky="n")
        spacing_frame.grid_propagate(False)
        
        prestress_frame = tk.Frame(param_entries_frame, width=140, bg=BG_COLOR)
        prestress_frame.grid(row=0, column=2, padx=5, sticky="n")
        prestress_frame.grid_propagate(False)
        
        # Section-Unit combo component
        self.create_section_unit_component(section_unit_frame)
        
        # Spacing component
        self.create_spacing_component(spacing_frame)
        
        # Prestress component (for both N and F types)
        if self.anchor_type in ["N", "F"]:
            self.create_prestress_component(prestress_frame)
        
        # Criteria frame for settlement
        criteria_frame = tk.LabelFrame(self.parameter_frame, text="Criteria", bg=BG_COLOR)
        criteria_frame.grid(row=1, column=0, sticky="ew", padx=5, pady=5)
        
        # Settlement component
        self.create_settlement_component(criteria_frame)
    
    def create_g_parameter_ui(self):
        """Create parameter UI for G type anchors."""
        # Create parameter entries frame
        param_entries_frame = tk.Frame(self.parameter_frame, bg=BG_COLOR)
        param_entries_frame.grid(row=0, column=0, sticky="ew", padx=5, pady=5)
        
        # Create subframes for G-type specific parameters
        section_unit_frame = tk.Frame(param_entries_frame, width=180, bg=BG_COLOR)
        section_unit_frame.grid(row=0, column=0, padx=5, sticky="n")
        section_unit_frame.grid_propagate(False)
        
        spacing_frame = tk.Frame(param_entries_frame, width=140, bg=BG_COLOR)
        spacing_frame.grid(row=0, column=1, padx=5, sticky="n")
        spacing_frame.grid_propagate(False)
        
        theta_s_frame = tk.Frame(param_entries_frame, width=140, bg=BG_COLOR)
        theta_s_frame.grid(row=0, column=2, padx=5, sticky="n")
        theta_s_frame.grid_propagate(False)
        
        theta_g_frame = tk.Frame(param_entries_frame, width=140, bg=BG_COLOR)
        theta_g_frame.grid(row=0, column=3, padx=5, sticky="n")
        theta_g_frame.grid_propagate(False)
        
        anchor_length_frame = tk.Frame(param_entries_frame, width=140, bg=BG_COLOR)
        anchor_length_frame.grid(row=0, column=4, padx=5, sticky="n")
        anchor_length_frame.grid_propagate(False)
        
        # Section-Unit combo component
        self.create_section_unit_component(section_unit_frame)
        
        # Spacing component
        self.create_spacing_component(spacing_frame)
        
        # G-type specific parameters
        self.create_theta_s_component(theta_s_frame)
        self.create_theta_g_component(theta_g_frame)
        self.create_anchor_length_component(anchor_length_frame)
        
        # Criteria frame for settlement
        criteria_frame = tk.LabelFrame(self.parameter_frame, text="Criteria", bg=BG_COLOR)
        criteria_frame.grid(row=1, column=0, sticky="ew", padx=5, pady=5)
        
        # Settlement component
        self.create_settlement_component(criteria_frame)
    
    def create_section_unit_component(self, parent_frame: tk.Frame):
        """Create section-unit combination selector."""
        # Import required functions and constants
        from .anchor_param_def import load_steel_sections, create_combo_selection_component
        
        # Load section options based on anchor type
        section_options = load_steel_sections(self.anchor_type)
        
        # Get default combinations based on anchor type
        from .anchor_param_class import DEFAULT_ANCHOR_VALUES
        default_combos = DEFAULT_ANCHOR_VALUES.get(self.anchor_type, {}).get("combos", [("T40", 1)])
        
        # Create combo selection component with add/remove functionality
        combo_data = create_combo_selection_component(
            parent_frame, section_options, UNIT_OPTIONS, default_combos
        )
        
        # Store component data for later access
        self.parameter_ui_components['combo_vars'] = combo_data.vars_list
        self.parameter_ui_components['combo_entries'] = combo_data.entries_list
        self.parameter_ui_components['combo_frame'] = combo_data.parent_frame
    
    def create_spacing_component(self, parent_frame: tk.Frame):
        """Create spacing parameter component with add/remove functionality."""
        from .anchor_param_def import create_dynamic_entry_component
        from .anchor_param_class import DEFAULT_ANCHOR_VALUES
        
        # Get default spacing values based on anchor type
        default_spacing = DEFAULT_ANCHOR_VALUES.get(self.anchor_type, {}).get("spacing", ["3"])
        
        # Create dynamic entry component with add/remove buttons
        spacing_data = create_dynamic_entry_component(
            parent_frame, "Spacing (m)", default_spacing
        )
        
        # Store component data
        self.parameter_ui_components['spacing_vars'] = spacing_data.vars_list
        self.parameter_ui_components['spacing_entries'] = spacing_data.entries_list
        self.parameter_ui_components['spacing_frame'] = spacing_data.parent_frame
    
    def create_prestress_component(self, parent_frame: tk.Frame):
        """Create prestress parameter component with add/remove functionality."""
        from .anchor_param_def import create_dynamic_entry_component
        
        # Create dynamic entry component with add/remove buttons
        prestress_data = create_dynamic_entry_component(
            parent_frame, "Prestress (kN/m)", ["0"]
        )
        
        # Store component data
        self.parameter_ui_components['prestress_vars'] = prestress_data.vars_list
        self.parameter_ui_components['prestress_entries'] = prestress_data.entries_list
        self.parameter_ui_components['prestress_frame'] = prestress_data.parent_frame
    
    def create_theta_s_component(self, parent_frame: tk.Frame):
        """Create theta_s parameter component for G-type anchors with add/remove functionality."""
        from .anchor_param_def import create_dynamic_entry_component
        from .anchor_param_class import DEFAULT_ANCHOR_VALUES
        
        # Get default theta_s values for G-type anchors
        default_theta_s = DEFAULT_ANCHOR_VALUES.get("G", {}).get("theta_s", ["45"])
        
        # Create dynamic entry component with add/remove buttons
        theta_s_data = create_dynamic_entry_component(
            parent_frame, "Theta_s (deg)", default_theta_s
        )
        
        # Store component data
        self.parameter_ui_components['theta_s_vars'] = theta_s_data.vars_list
        self.parameter_ui_components['theta_s_entries'] = theta_s_data.entries_list
        self.parameter_ui_components['theta_s_frame'] = theta_s_data.parent_frame
    
    def create_theta_g_component(self, parent_frame: tk.Frame):
        """Create theta_g parameter component for G-type anchors with add/remove functionality."""
        from .anchor_param_def import create_dynamic_entry_component
        from .anchor_param_class import DEFAULT_ANCHOR_VALUES
        
        # Get default theta_g values for G-type anchors
        default_theta_g = DEFAULT_ANCHOR_VALUES.get("G", {}).get("theta_g", ["70", "65", "60", "55", "50", "45"])
        
        # Create dynamic entry component with add/remove buttons
        theta_g_data = create_dynamic_entry_component(
            parent_frame, "Theta_g (deg)", default_theta_g
        )
        
        # Store component data
        self.parameter_ui_components['theta_g_vars'] = theta_g_data.vars_list
        self.parameter_ui_components['theta_g_entries'] = theta_g_data.entries_list
        self.parameter_ui_components['theta_g_frame'] = theta_g_data.parent_frame
    
    def create_anchor_length_component(self, parent_frame: tk.Frame):
        """Create anchor length parameter component for G-type anchors with add/remove functionality."""
        from .anchor_param_def import create_dynamic_entry_component
        from .anchor_param_class import DEFAULT_ANCHOR_VALUES
        
        # Get default anchor length values for G-type anchors
        default_anchor_length = DEFAULT_ANCHOR_VALUES.get("G", {}).get("anchor_length", ["10", "11", "13", "15", "15.5"])
        
        # Create dynamic entry component with add/remove buttons
        anchor_length_data = create_dynamic_entry_component(
            parent_frame, "Anchor Length (m)", default_anchor_length
        )
        
        # Store component data
        self.parameter_ui_components['anchor_length_vars'] = anchor_length_data.vars_list
        self.parameter_ui_components['anchor_length_entries'] = anchor_length_data.entries_list
        self.parameter_ui_components['anchor_length_frame'] = anchor_length_data.parent_frame
    
    def create_settlement_component(self, parent_frame: tk.Frame):
        """Create settlement configuration component with add/remove functionality."""
        from .anchor_param_def import create_settlement_component
        
        # Create settlement component with add/remove buttons
        settlement_data = create_settlement_component(parent_frame)
        
        # Store component data
        self.parameter_ui_components['settlement_vars'] = settlement_data.vars_list
        self.parameter_ui_components['settlement_entries'] = settlement_data.entries_list
        self.parameter_ui_components['settlement_frame'] = settlement_data.parent_frame
    
    def add_settlement_row(self):
        """Add a new settlement row using the parameter module's functionality."""
        from .anchor_param_def import add_settlement_row
        
        # Get the settlement component data
        if 'settlement_vars' in self.parameter_ui_components:
            # Create a UIComponentData structure for compatibility
            from .anchor_param_class import UIComponentData
            settlement_component = UIComponentData(
                self.parameter_ui_components['settlement_vars'],
                self.parameter_ui_components['settlement_entries'],
                self.parameter_ui_components['settlement_frame']
            )
            
            # Use the existing add_settlement_row function
            add_settlement_row(settlement_component)
            
            # Update stored references
            self.parameter_ui_components['settlement_vars'] = settlement_component.vars_list
            self.parameter_ui_components['settlement_entries'] = settlement_component.entries_list
    
    def create_control_buttons(self):
        """Create control buttons for coordinate set management."""
        # Create buttons frame
        buttons_frame = tk.Frame(self.geometry_frame, bg=BG_COLOR)
        buttons_frame.grid(row=1000, column=0, sticky="ew", padx=5, pady=10)
        
        # Add set button
        self.add_button = create_primary_button(
            buttons_frame,
            "Add Coordinate Set",
            self.add_coordinate_set,
            width=18, height=1
        )
        
        # Remove set button
        self.remove_button = create_danger_button(
            buttons_frame,
            "Remove Coordinate Set",
            self.remove_coordinate_set,
            width=18, height=1
        )
        
        # Update button states
        self.update_button_states()
    
    def add_coordinate_set(self):
        """Add a new coordinate set to this anchor."""
        set_number = len(self.coordinate_sets) + 1
        row = len(self.coordinate_sets) + 1  # Start from row 1 (row 0 is for anchor name)
        
        # Use appropriate coordinate set based on anchor type
        if self.anchor_name.startswith('G'):
            # G-type anchors only need X1, Y1
            coord_set = GTypeCoordinateSet(self.geometry_frame, set_number, row)
        else:
            # N-type and F-type anchors need X1, Y1, X2, Y2
            coord_set = CoordinateSet(self.geometry_frame, set_number, row)
        
        self.coordinate_sets.append(coord_set)
        self.update_button_states()
        
        # Sync with paired anchor if applicable
        self.sync_with_paired_anchor("add")
        
        logging.info(f"Added coordinate set {set_number} to {self.anchor_name}")
    
    def remove_coordinate_set(self):
        """Remove the last coordinate set from this anchor."""
        if len(self.coordinate_sets) <= 1:
            messagebox.showwarning("Cannot Remove",
                                 f"At least one coordinate set is required for {self.anchor_name}")
            return
        
        # Remove the last coordinate set
        coord_set = self.coordinate_sets.pop()
        coord_set.destroy()
        
        self.update_button_states()
        
        # Sync with paired anchor if applicable
        self.sync_with_paired_anchor("remove")
        
        logging.info(f"Removed coordinate set from {self.anchor_name}")
    
    def update_button_states(self):
        """Update the state of add/remove buttons."""
        current_count = len(self.coordinate_sets)
        
        # Remove button: disabled if only have 1 set
        if self.remove_button:
            update_button_state(self.remove_button, current_count > 1, ERROR_COLOR)
    
    def sync_with_paired_anchor(self, action: str):
        """Sync coordinate sets with paired anchor to maintain same number of sets."""
        if self._syncing or not self.ui_components_manager:
            return
        
        paired_anchor_name = self.get_paired_anchor_name()
        if not paired_anchor_name:
            return
        
        # Check if paired anchor exists
        if paired_anchor_name not in self.ui_components_manager.anchor_sections:
            return
        
        paired_anchor = self.ui_components_manager.anchor_sections[paired_anchor_name]
        
        # Set syncing flag to prevent infinite recursion
        self._syncing = True
        paired_anchor._syncing = True
        
        try:
            current_sets = len(self.coordinate_sets)
            paired_sets = len(paired_anchor.coordinate_sets)
            
            if action == "add" and current_sets > paired_sets:
                # Add sets to paired anchor to match current anchor
                while len(paired_anchor.coordinate_sets) < current_sets:
                    paired_anchor._add_coordinate_set_internal()
            elif action == "remove" and current_sets < paired_sets:
                # Remove sets from paired anchor to match current anchor
                while len(paired_anchor.coordinate_sets) > current_sets and len(paired_anchor.coordinate_sets) > 1:
                    paired_anchor._remove_coordinate_set_internal()
            
            # Update button states for both anchors
            paired_anchor.update_button_states()
            
        finally:
            # Clear syncing flags
            self._syncing = False
            paired_anchor._syncing = False
    
    def _add_coordinate_set_internal(self):
        """Add a new coordinate set without triggering synchronization."""
        set_number = len(self.coordinate_sets) + 1
        row = len(self.coordinate_sets) + 1
        
        # Use appropriate coordinate set based on anchor type
        if self.anchor_name.startswith('G'):
            coord_set = GTypeCoordinateSet(self.geometry_frame, set_number, row)
        else:
            coord_set = CoordinateSet(self.geometry_frame, set_number, row)
        
        self.coordinate_sets.append(coord_set)
        self.update_button_states()
    
    def _remove_coordinate_set_internal(self):
        """Remove the last coordinate set without triggering synchronization."""
        if len(self.coordinate_sets) <= 1:
            return
        
        coord_set = self.coordinate_sets.pop()
        coord_set.destroy()
        self.update_button_states()
    
    def get_paired_anchor_name(self) -> Optional[str]:
        """Get the name of the paired anchor (F1<->F2, G1<->G2)."""
        if self.anchor_name == "F1":
            return "F2"
        elif self.anchor_name == "F2":
            return "F1"
        elif self.anchor_name == "G1":
            return "G2"
        elif self.anchor_name == "G2":
            return "G1"
        return None
    
    def get_all_values(self) -> UnifiedAnchorData:
        """Get all coordinate and parameter values from this anchor."""
        # Get geometry data
        geometry_coordinates = []
        for coord_set in self.coordinate_sets:
            try:
                values = coord_set.get_values()
                geometry_coordinates.append(values)
            except ValueError as e:
                raise ValueError(f"Error in {self.anchor_name}: {str(e)}")
        
        geometry_data = {
            "anchor_name": self.anchor_name_var.get() if self.anchor_name_var else self.anchor_name,
            "coordinates": geometry_coordinates
        }
        
        # Get parameter data
        parameter_data = {}
        
        # Section-unit combinations
        if 'combo_vars' in self.parameter_ui_components:
            parameter_data['sections'] = []
            for combo in self.parameter_ui_components['combo_vars']:
                parameter_data['sections'].append({
                    'section': combo['section'].get(),
                    'unit': combo['unit'].get()
                })
        
        # Spacing
        if 'spacing_vars' in self.parameter_ui_components:
            parameter_data['spacing'] = [var.get() for var in self.parameter_ui_components['spacing_vars']]
        
        # Prestress (N-type)
        if 'prestress_vars' in self.parameter_ui_components:
            parameter_data['prestress'] = [var.get() for var in self.parameter_ui_components['prestress_vars']]
        
        # G-type specific parameters
        if 'theta_s_vars' in self.parameter_ui_components:
            parameter_data['theta_s'] = [var.get() for var in self.parameter_ui_components['theta_s_vars']]
        
        if 'theta_g_vars' in self.parameter_ui_components:
            parameter_data['theta_g'] = [var.get() for var in self.parameter_ui_components['theta_g_vars']]
        
        if 'anchor_length_vars' in self.parameter_ui_components:
            parameter_data['anchor_length'] = [var.get() for var in self.parameter_ui_components['anchor_length_vars']]
        
        # Settlement data
        if 'settlement_vars' in self.parameter_ui_components:
            parameter_data['settlement'] = []
            for vars_dict in self.parameter_ui_components['settlement_vars']:
                settlement_row = {col: var.get() for col, var in vars_dict.items()}
                parameter_data['settlement'].append(settlement_row)
        
        return UnifiedAnchorData(
            name=geometry_data["anchor_name"],
            anchor_type=self.anchor_type,
            geometry_data=geometry_data,
            parameter_data=parameter_data
        )
    
    def set_anchor_name(self, name: str):
        """Set the anchor name."""
        if self.anchor_name_var:
            self.anchor_name_var.set(name)
    
    def get_anchor_name(self) -> str:
        """Get the current anchor name."""
        return self.anchor_name_var.get() if self.anchor_name_var else self.anchor_name
    
    def clear_all_sets(self):
        """Clear all coordinate sets."""
        for coord_set in self.coordinate_sets:
            coord_set.destroy()
        self.coordinate_sets.clear()

    def set_parameter_values(self, parameter_data: Dict[str, Any]):
        """
        Set parameter values from saved data.

        Args:
            parameter_data: Dictionary containing parameter data to set
        """
        try:
            # Set section-unit combinations
            if 'sections' in parameter_data and 'combo_vars' in self.parameter_ui_components:
                sections_data = parameter_data['sections']
                combo_vars = self.parameter_ui_components['combo_vars']

                for i, section_data in enumerate(sections_data):
                    if i < len(combo_vars):
                        combo_vars[i]['section'].set(section_data.get('section', ''))
                        combo_vars[i]['unit'].set(section_data.get('unit', ''))

            # Set spacing values
            if 'spacing' in parameter_data and 'spacing_vars' in self.parameter_ui_components:
                spacing_data = parameter_data['spacing']
                spacing_vars = self.parameter_ui_components['spacing_vars']

                for i, spacing_value in enumerate(spacing_data):
                    if i < len(spacing_vars):
                        spacing_vars[i].set(str(spacing_value))

            # Set prestress values (N-type)
            if 'prestress' in parameter_data and 'prestress_vars' in self.parameter_ui_components:
                prestress_data = parameter_data['prestress']
                prestress_vars = self.parameter_ui_components['prestress_vars']

                for i, prestress_value in enumerate(prestress_data):
                    if i < len(prestress_vars):
                        prestress_vars[i].set(str(prestress_value))

            # Set length values (G-type)
            if 'length' in parameter_data and 'length_vars' in self.parameter_ui_components:
                length_data = parameter_data['length']
                length_vars = self.parameter_ui_components['length_vars']

                for i, length_value in enumerate(length_data):
                    if i < len(length_vars):
                        length_vars[i].set(str(length_value))

            # Set angle values (G-type)
            if 'angle' in parameter_data and 'angle_vars' in self.parameter_ui_components:
                angle_data = parameter_data['angle']
                angle_vars = self.parameter_ui_components['angle_vars']

                for i, angle_value in enumerate(angle_data):
                    if i < len(angle_vars):
                        angle_vars[i].set(str(angle_value))

            # Set grout body values (G-type)
            if 'grout_body' in parameter_data and 'grout_body_vars' in self.parameter_ui_components:
                grout_body_data = parameter_data['grout_body']
                grout_body_vars = self.parameter_ui_components['grout_body_vars']

                for i, grout_value in enumerate(grout_body_data):
                    if i < len(grout_body_vars):
                        grout_body_vars[i].set(str(grout_value))

            # Set settlement data
            if 'settlement' in parameter_data and 'settlement_vars' in self.parameter_ui_components:
                settlement_data = parameter_data['settlement']
                settlement_vars = self.parameter_ui_components['settlement_vars']

                for i, settlement_row in enumerate(settlement_data):
                    if i < len(settlement_vars):
                        for col, value in settlement_row.items():
                            if col in settlement_vars[i]:
                                settlement_vars[i][col].set(str(value))

            logging.info(f"Set parameter values for anchor section {self.anchor_name}")

        except Exception as e:
            logging.error(f"Error setting parameter values for {self.anchor_name}: {e}")

    def destroy(self):
        """Remove this entire anchor section."""
        if self.frame:
            self.frame.destroy()
        self.coordinate_sets.clear()
        self.parameter_ui_components.clear()


class UnifiedAnchorUIComponents:
    """
    Manages UI components for unified anchor configuration.
    
    This class extends GeometryUIComponents to include parameter management
    capabilities for a unified anchor editing interface.
    """
    
    def __init__(self):
        # Anchor type selection
        self.anchor_type_var = None
        self.anchor_sections = {}  # Dictionary: anchor_name -> UnifiedAnchorSection
        self.content_frame = None
        self.current_anchor_type = None
        
        # UI management
        self.anchor_management_frame = None
        self.add_anchor_button = None
        self.remove_anchor_button = None
        self.status_label = None
        self.stage_label = None
        
        # Data references
        self.excel_master = None
        self.selected_stage = None
        
        # Parameter-specific storage
        self.anchor_ui_components = {}  # For compatibility with existing parameter code
    
    def clear_all_anchors(self):
        """Clear all anchor sections."""
        for anchor_section in self.anchor_sections.values():
            anchor_section.destroy()
        self.anchor_sections.clear()
        self.anchor_ui_components.clear()
    
    def add_anchor_section(self, anchor_name: str) -> UnifiedAnchorSection:
        """Add a new unified anchor section."""
        if anchor_name in self.anchor_sections:
            logging.warning(f"Anchor section {anchor_name} already exists")
            return self.anchor_sections[anchor_name]
        
        row = len(self.anchor_sections)
        anchor_type = self.anchor_type_var.get() if self.anchor_type_var else "N"
        
        anchor_section = UnifiedAnchorSection(
            self.content_frame, anchor_name, row, self, anchor_type
        )
        self.anchor_sections[anchor_name] = anchor_section
        
        # Update compatibility storage
        self.anchor_ui_components[anchor_name] = anchor_section.parameter_ui_components
        
        return anchor_section
    
    def remove_anchor_section(self, anchor_name: str):
        """Remove an anchor section."""
        if anchor_name in self.anchor_sections:
            self.anchor_sections[anchor_name].destroy()
            del self.anchor_sections[anchor_name]
            
            # Remove from compatibility storage
            if anchor_name in self.anchor_ui_components:
                del self.anchor_ui_components[anchor_name]
            
            # Reorganize remaining sections
            self._reorganize_anchor_sections()
    
    def _reorganize_anchor_sections(self):
        """Reorganize anchor sections after removal."""
        for i, (anchor_name, anchor_section) in enumerate(self.anchor_sections.items()):
            anchor_section.row = i
            anchor_section.frame.grid(row=i, column=0, sticky="ew", padx=10, pady=10)
    
    def get_all_anchor_data(self) -> Dict[str, UnifiedAnchorData]:
        """Get all unified anchor data from all sections."""
        all_data = {}
        for anchor_name, anchor_section in self.anchor_sections.items():
            all_data[anchor_name] = anchor_section.get_all_values()
        return all_data
    
    def get_geometry_data(self) -> Dict[str, Dict[str, Any]]:
        """Get geometry data in the format expected by existing code."""
        geometry_data = {"anchors": {}}
        for anchor_name, anchor_section in self.anchor_sections.items():
            unified_data = anchor_section.get_all_values()
            geometry_data["anchors"][anchor_name] = unified_data.geometry_data
        
        if self.anchor_type_var:
            geometry_data["anchor_type"] = self.anchor_type_var.get()
        
        return geometry_data
    
    def get_parameter_data(self) -> Dict[str, Any]:
        """Get parameter data in the format expected by existing code."""
        parameter_data = {}
        for anchor_name, anchor_section in self.anchor_sections.items():
            unified_data = anchor_section.get_all_values()
            parameter_data[anchor_name] = unified_data.parameter_data
        
        return parameter_data
    
    def update_anchor_management_buttons(self):
        """Update the state of anchor management buttons (for F2/G2)."""
        anchor_type = self.anchor_type_var.get() if self.anchor_type_var else None
        
        if not self.add_anchor_button or not self.remove_anchor_button:
            return
        
        if anchor_type == "F":
            # For type F: can add F2 if not present
            self.anchor_management_frame.pack(fill="x", padx=20, pady=10)
            if "F2" not in self.anchor_sections:
                self.add_anchor_button.config(state="normal", text="Add F2 Anchor", bg=ACCENT_COLOR)
                self.remove_anchor_button.config(state="disabled", bg="#CCCCCC")
            else:
                self.add_anchor_button.config(state="disabled", bg="#CCCCCC")
                self.remove_anchor_button.config(state="normal", text="Remove F2 Anchor", bg=ERROR_COLOR)
        
        elif anchor_type == "G":
            # For type G: can add G2 if not present
            self.anchor_management_frame.pack(fill="x", padx=20, pady=10)
            if "G2" not in self.anchor_sections:
                self.add_anchor_button.config(state="normal", text="Add G2 Anchor", bg=ACCENT_COLOR)
                self.remove_anchor_button.config(state="disabled", bg="#CCCCCC")
            else:
                self.add_anchor_button.config(state="disabled", bg="#CCCCCC")
                self.remove_anchor_button.config(state="normal", text="Remove G2 Anchor", bg=ERROR_COLOR)
        
        else:  # Type N
            # For type N: no additional anchors allowed - hide the entire frame
            self.anchor_management_frame.pack_forget()