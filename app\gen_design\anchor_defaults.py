"""
<PERSON><PERSON> Defaults Storage Module

This module provides functionality for storing and loading persistent default values
for anchor configurations. It allows users to save their commonly used anchor settings
and automatically load them as defaults in future sessions.
"""

import json
import logging
import os
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime

# Storage settings
DEFAULTS_DIR = os.path.join("C:\\ELS_RPA", "anchor_defaults")
DEFAULTS_FILE_TEMPLATE = "anchor_defaults_{}.json"


def ensure_defaults_directory() -> bool:
    """
    Ensure the anchor defaults directory exists.
    
    Returns:
        True if directory exists or was created successfully, False otherwise
    """
    try:
        os.makedirs(DEFAULTS_DIR, exist_ok=True)
        return True
    except Exception as e:
        logging.error(f"Error creating anchor defaults directory: {e}")
        return False


def get_defaults_file_path(anchor_type: str) -> str:
    """
    Get the file path for storing defaults for a specific anchor type.
    
    Args:
        anchor_type: The anchor type (N, F, G)
        
    Returns:
        Full path to the defaults file for the anchor type
    """
    filename = DEFAULTS_FILE_TEMPLATE.format(anchor_type.upper())
    return os.path.join(DEFAULTS_DIR, filename)


def save_anchor_defaults(anchor_type: str, unified_data: Dict[str, Any]) -> bool:
    """
    Save anchor defaults for a specific anchor type.
    
    Args:
        anchor_type: The anchor type (N, F, G)
        unified_data: The unified anchor data to save as defaults
        
    Returns:
        True if saved successfully, False otherwise
    """
    try:
        if not ensure_defaults_directory():
            return False
        
        # Prepare the data structure for storage
        defaults_data = {
            "anchor_type": anchor_type.upper(),
            "saved_timestamp": datetime.now().isoformat(),
            "defaults": {}
        }
        
        # Extract and store the relevant data from unified_data
        for anchor_name, anchor_data in unified_data.items():
            if hasattr(anchor_data, 'geometry_data') and hasattr(anchor_data, 'parameter_data'):
                # Store both geometry and parameter data
                defaults_data["defaults"][anchor_name] = {
                    "name": anchor_data.name,
                    "geometry_data": anchor_data.geometry_data,
                    "parameter_data": anchor_data.parameter_data
                }
            else:
                # Handle dictionary format
                defaults_data["defaults"][anchor_name] = anchor_data
        
        # Write to file
        file_path = get_defaults_file_path(anchor_type)
        with open(file_path, 'w') as f:
            json.dump(defaults_data, f, indent=2)
        
        logging.info(f"Anchor defaults saved for type {anchor_type} to {file_path}")
        return True
        
    except Exception as e:
        logging.error(f"Error saving anchor defaults for type {anchor_type}: {e}")
        return False


def load_anchor_defaults(anchor_type: str) -> Optional[Dict[str, Any]]:
    """
    Load anchor defaults for a specific anchor type.
    
    Args:
        anchor_type: The anchor type (N, F, G)
        
    Returns:
        Dictionary containing the default anchor data, or None if not available
    """
    try:
        file_path = get_defaults_file_path(anchor_type)
        
        if not os.path.exists(file_path):
            logging.info(f"No defaults file found for anchor type {anchor_type}")
            return None
        
        with open(file_path, 'r') as f:
            defaults_data = json.load(f)
        
        # Validate the data structure
        if not isinstance(defaults_data, dict) or "defaults" not in defaults_data:
            logging.warning(f"Invalid defaults file format for anchor type {anchor_type}")
            return None
        
        # Return the defaults data
        saved_defaults = defaults_data["defaults"]
        logging.info(f"Loaded anchor defaults for type {anchor_type} from {file_path}")
        logging.info(f"Available default anchors: {list(saved_defaults.keys())}")
        
        return saved_defaults
        
    except Exception as e:
        logging.error(f"Error loading anchor defaults for type {anchor_type}: {e}")
        return None


def get_default_coordinates_from_saved(anchor_type: str) -> List[Dict[str, float]]:
    """
    Extract coordinate data from saved defaults for a specific anchor type.
    
    Args:
        anchor_type: The anchor type (N, F, G)
        
    Returns:
        List of coordinate dictionaries, or empty list if no defaults available
    """
    try:
        saved_defaults = load_anchor_defaults(anchor_type)
        if not saved_defaults:
            return []
        
        coordinates = []
        
        # Extract coordinates from the first available anchor section
        for anchor_name, anchor_data in saved_defaults.items():
            geometry_data = anchor_data.get("geometry_data", {})
            if "coordinates" in geometry_data:
                coordinates.extend(geometry_data["coordinates"])
                break  # Use coordinates from first anchor section
        
        logging.info(f"Extracted {len(coordinates)} coordinate sets from saved defaults for type {anchor_type}")
        return coordinates
        
    except Exception as e:
        logging.error(f"Error extracting coordinates from saved defaults for type {anchor_type}: {e}")
        return []


def get_default_parameters_from_saved(anchor_type: str, anchor_section: str) -> Optional[Dict[str, Any]]:
    """
    Extract parameter data from saved defaults for a specific anchor type and section.
    
    Args:
        anchor_type: The anchor type (N, F, G)
        anchor_section: The anchor section (N, F1, F2, G1, G2)
        
    Returns:
        Dictionary containing parameter data, or None if not available
    """
    try:
        saved_defaults = load_anchor_defaults(anchor_type)
        if not saved_defaults:
            return None
        
        # Look for the specific anchor section
        if anchor_section in saved_defaults:
            parameter_data = saved_defaults[anchor_section].get("parameter_data", {})
            logging.info(f"Found saved parameter defaults for {anchor_section}")
            return parameter_data
        
        # If specific section not found, try to use the first available section
        for anchor_name, anchor_data in saved_defaults.items():
            parameter_data = anchor_data.get("parameter_data", {})
            if parameter_data:
                logging.info(f"Using parameter defaults from {anchor_name} for {anchor_section}")
                return parameter_data
        
        logging.info(f"No parameter defaults found for {anchor_section}")
        return None
        
    except Exception as e:
        logging.error(f"Error extracting parameters from saved defaults for {anchor_section}: {e}")
        return None


def clear_anchor_defaults(anchor_type: str) -> bool:
    """
    Clear saved defaults for a specific anchor type.
    
    Args:
        anchor_type: The anchor type (N, F, G)
        
    Returns:
        True if cleared successfully, False otherwise
    """
    try:
        file_path = get_defaults_file_path(anchor_type)
        
        if os.path.exists(file_path):
            os.remove(file_path)
            logging.info(f"Cleared anchor defaults for type {anchor_type}")
            return True
        else:
            logging.info(f"No defaults file to clear for anchor type {anchor_type}")
            return True
        
    except Exception as e:
        logging.error(f"Error clearing anchor defaults for type {anchor_type}: {e}")
        return False


def list_available_defaults() -> Dict[str, bool]:
    """
    Check which anchor types have saved defaults available.
    
    Returns:
        Dictionary mapping anchor types to availability status
    """
    availability = {}
    
    for anchor_type in ["N", "F", "G"]:
        file_path = get_defaults_file_path(anchor_type)
        availability[anchor_type] = os.path.exists(file_path)
    
    return availability


def get_defaults_info(anchor_type: str) -> Optional[Dict[str, Any]]:
    """
    Get information about saved defaults for a specific anchor type.
    
    Args:
        anchor_type: The anchor type (N, F, G)
        
    Returns:
        Dictionary containing defaults information, or None if not available
    """
    try:
        file_path = get_defaults_file_path(anchor_type)
        
        if not os.path.exists(file_path):
            return None
        
        with open(file_path, 'r') as f:
            defaults_data = json.load(f)
        
        info = {
            "anchor_type": defaults_data.get("anchor_type"),
            "saved_timestamp": defaults_data.get("saved_timestamp"),
            "anchor_sections": list(defaults_data.get("defaults", {}).keys()),
            "file_path": file_path
        }
        
        return info
        
    except Exception as e:
        logging.error(f"Error getting defaults info for type {anchor_type}: {e}")
        return None
